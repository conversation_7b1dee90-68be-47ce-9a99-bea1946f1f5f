import { MyUserProfileType } from "@/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";
import React, { useTransition, useState, useRef } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  View,
  Pressable,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
} from "react-native";

import { Feather } from "@expo/vector-icons";
import ButtonCircle from "@/components/ButtonCircle";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";

type Props = {
  onLogout: () => void;
  onDelete: () => void;
  user?: MyUserProfileType | null;
  isLogoutPending?: boolean;
};

const styles = StyleSheet.create({
  buttonOutline: {
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
  },
  dropdownTrigger: {
    width: 32,
    height: 32,
    borderRadius: globalStyles.rounded.full,
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
    justifyContent: "center",
    alignItems: "center",
  },
  dropdownOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  dropdownContainer: {
    position: "absolute",
    backgroundColor: "white",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 150,
    maxWidth: 200,
    right: 20,
    top: 80,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  dropdownItemText: {
    fontSize: 16,
    color: globalStyles.colors.dark.secondary,
  },
});

const ProfileStatsAction = ({ onLogout, onDelete }: Props) => {
  const { t } = useTranslation();
  const [isLogoutPending, startLogoutTransition] = useTransition();
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const dropdownRef = useRef<View>(null);

  const dropdownItems = [
    {
      id: "settings",
      label: t("common.settings"),
      icon: "settings",
      onPress: () => {
        setIsDropdownVisible(false);
        router.push("/settings");
      },
    },
    {
      id: "logout",
      label: t("auth.sign_out"),
      icon: "log-out",
      onPress: () => {
        setIsDropdownVisible(false);
        startLogoutTransition(() => onLogout());
      },
    },
    {
      id: "delete",
      label: t("common.delete"),
      icon: "trash",
      onPress: () => {
        setIsDropdownVisible(false);
        onDelete();
      },
    },
  ];

  const renderDropdownItem = ({ item }: { item: any }) => (
    <Pressable
      style={styles.dropdownItem}
      onPress={item.onPress}
      android_ripple={{
        color: globalStyles.colors.light.primary,
        borderless: false,
      }}
    >
      {item.id === "logout" && isLogoutPending ? (
        <ActivityIndicator size="small" color={globalStyles.rgba().primary1} />
      ) : (
        <Feather
          name={item.icon}
          size={16}
          color={globalStyles.rgba().primary1}
        />
      )}
      <Text style={styles.dropdownItemText}>{item.label}</Text>
    </Pressable>
  );

  return (
    <>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <View>
          <Text
            style={{
              fontSize: globalStyles.size["2xl"],
              fontWeight: "600",
              color: globalStyles.colors.light.secondary,
            }}
          >
            {t("event.favorite_events")}
          </Text>
        </View>

        <View style={{ flexDirection: "row", gap: globalStyles.gap["2xs"] }}>
          <ButtonCircle
            style={{
              backgroundColor: globalStyles.colors.primary1,
            }}
            onPress={() => router.push("/profile-edit")}
          >
            <Feather name="edit" size={14} color={globalStyles.colors.white} />
          </ButtonCircle>

          <Pressable
            ref={dropdownRef}
            style={styles.dropdownTrigger}
            onPress={() => setIsDropdownVisible(!isDropdownVisible)}
            android_ripple={{
              color: globalStyles.colors.light.primary,
              radius: globalStyles.rounded.full,
              borderless: true,
            }}
          >
            <Feather
              name="more-horizontal"
              size={16}
              color={globalStyles.rgba().primary1}
            />
          </Pressable>
        </View>
      </View>

      {isDropdownVisible && (
        <Modal
          transparent={true}
          visible={isDropdownVisible}
          animationType="fade"
          onRequestClose={() => setIsDropdownVisible(false)}
        >
          <TouchableWithoutFeedback onPress={() => setIsDropdownVisible(false)}>
            <View style={styles.dropdownOverlay}>
              <View style={styles.dropdownContainer}>
                <FlatList
                  data={dropdownItems}
                  renderItem={renderDropdownItem}
                  keyExtractor={(item) => item.id}
                  showsVerticalScrollIndicator={false}
                  style={{ flexGrow: 0 }}
                  contentContainerStyle={{ flexGrow: 0 }}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </>
  );
};

export default ProfileStatsAction;
