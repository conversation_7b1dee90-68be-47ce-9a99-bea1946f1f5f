import { MyUserProfileType } from "@/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";
import React, { useTransition, useState } from "react";
import { ActivityIndicator, StyleSheet, Text, View } from "react-native";

import { Feather } from "@expo/vector-icons";
import ButtonCircle from "@/components/ButtonCircle";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";
import { Dropdown } from "react-native-element-dropdown";

type Props = {
  onLogout: () => void;
  onDelete: () => void;
  user?: MyUserProfileType | null;
  isLogoutPending?: boolean;
};

const styles = StyleSheet.create({
  buttonOutline: {
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
  },
  dropdown: {
    width: 32,
    height: 32,
    borderRadius: globalStyles.rounded.full,
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
    paddingHorizontal: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  dropdownContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 150,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  dropdownItemText: {
    fontSize: 16,
    color: globalStyles.colors.dark.secondary,
  },
});

const ProfileStatsAction = ({ onLogout, onDelete }: Props) => {
  const { t } = useTranslation();
  const [isLogoutPending, startLogoutTransition] = useTransition();
  const [selectedValue, setSelectedValue] = useState(null);

  const dropdownItems = [
    {
      label: t("common.settings"),
      value: "settings",
      icon: "settings",
    },
    {
      label: t("auth.sign_out"),
      value: "logout",
      icon: "log-out",
    },
    {
      label: t("common.delete"),
      value: "delete",
      icon: "trash",
    },
  ];

  const handleDropdownChange = (item: any) => {
    setSelectedValue(null); // Reset selection immediately

    switch (item.value) {
      case "settings":
        router.push("/settings");
        break;
      case "logout":
        startLogoutTransition(() => onLogout());
        break;
      case "delete":
        onDelete();
        break;
    }
  };

  const renderItem = (item: any) => {
    return (
      <View style={styles.dropdownItem}>
        {item.value === "logout" && isLogoutPending ? (
          <ActivityIndicator
            size="small"
            color={globalStyles.rgba().primary1}
          />
        ) : (
          <Feather
            name={item.icon}
            size={16}
            color={globalStyles.rgba().primary1}
          />
        )}
        <Text style={styles.dropdownItemText}>{item.label}</Text>
      </View>
    );
  };

  const renderRightIcon = () => (
    <Feather
      name="more-horizontal"
      size={16}
      color={globalStyles.rgba().primary1}
    />
  );

  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      <View>
        <Text
          style={{
            fontSize: globalStyles.size["2xl"],
            fontWeight: "600",
            color: globalStyles.colors.light.secondary,
          }}
        >
          {t("event.favorite_events")}
        </Text>
      </View>

      <View style={{ flexDirection: "row", gap: globalStyles.gap["2xs"] }}>
        <ButtonCircle
          style={{
            backgroundColor: globalStyles.colors.primary1,
          }}
          onPress={() => router.push("/profile-edit")}
        >
          <Feather name="edit" size={14} color={globalStyles.colors.white} />
        </ButtonCircle>

        <Dropdown
          style={styles.dropdown}
          containerStyle={styles.dropdownContainer}
          data={dropdownItems}
          labelField="label"
          valueField="value"
          placeholder=""
          value={selectedValue}
          onChange={handleDropdownChange}
          renderRightIcon={renderRightIcon}
          renderItem={renderItem}
          dropdownPosition="bottom"
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default ProfileStatsAction;
