import { MyUserProfileType } from "@/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";
import React, { useTransition, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  Pressable,
} from "react-native";

import { Feather } from "@expo/vector-icons";
import ButtonCircle from "@/components/ButtonCircle";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";

type Props = {
  onLogout: () => void;
  onDelete: () => void;
  user?: MyUserProfileType | null;
  isLogoutPending?: boolean;
};

const styles = StyleSheet.create({
  buttonOutline: {
    borderWidth: 1,
    borderColor: globalStyles.colors.primary1,
    backgroundColor: "transparent",
  },
  dropdownOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    justifyContent: "flex-start",
    alignItems: "flex-end",
    paddingTop: 100,
    paddingRight: 20,
  },
  dropdownMenu: {
    backgroundColor: "white",
    borderRadius: 8,
    paddingVertical: 8,
    minWidth: 150,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  dropdownItemText: {
    fontSize: 16,
    color: globalStyles.colors.dark.secondary,
  },
});

const ProfileStatsAction = ({ onLogout, onDelete }: Props) => {
  const { t } = useTranslation();
  const [isLogoutPending, startLogoutTransition] = useTransition();
  const [showDropdown, setShowDropdown] = useState(false);

  const dropdownItems = [
    {
      icon: "edit",
      label: t("common.edit"),
      onPress: () => {
        setShowDropdown(false);
        router.push("/profile-edit");
      },
    },
    {
      icon: "settings",
      label: t("common.settings"),
      onPress: () => {
        setShowDropdown(false);
        router.push("/settings");
      },
    },
    {
      icon: "log-out",
      label: t("auth.sign_out"),
      onPress: () => {
        setShowDropdown(false);
        startLogoutTransition(() => onLogout());
      },
      isLoading: isLogoutPending,
    },
    {
      icon: "trash",
      label: t("common.delete"),
      onPress: () => {
        setShowDropdown(false);
        onDelete();
      },
    },
  ];

  return (
    <>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <View>
          <Text
            style={{
              fontSize: globalStyles.size["2xl"],
              fontWeight: "600",
              color: globalStyles.colors.light.secondary,
            }}
          >
            {t("event.favorite_events")}
          </Text>
        </View>

        <ButtonCircle
          style={styles.buttonOutline}
          onPress={() => setShowDropdown(true)}
        >
          <Feather
            name="more-horizontal"
            size={16}
            color={globalStyles.rgba().primary1}
          />
        </ButtonCircle>
      </View>

      <Modal
        visible={showDropdown}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDropdown(false)}
      >
        <Pressable
          style={styles.dropdownOverlay}
          onPress={() => setShowDropdown(false)}
        >
          <View style={styles.dropdownMenu}>
            {dropdownItems.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={styles.dropdownItem}
                onPress={item.onPress}
              >
                {item.isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={globalStyles.rgba().primary1}
                  />
                ) : (
                  <Feather
                    name={item.icon as any}
                    size={16}
                    color={globalStyles.rgba().primary1}
                  />
                )}
                <Text style={styles.dropdownItemText}>{item.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

export default ProfileStatsAction;
